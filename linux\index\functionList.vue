<template>
	<view class="function-list-container rd-32">
		<!-- 功能列表标题 -->
		<view class="function-list-header" v-if="title">
			<text class="header-title">{{ title }}</text>
			<view v-if="showEdit" class="edit-button ml-16" @click="handleEditClick">
				<uv-icon name="edit-pen" size="14" color="#909399" />
				<text class="edit-text">去编辑</text>
			</view>
		</view>

		<!-- 功能列表网格 -->
		<view class="function-grid" :class="gridClass">
			<view
				v-for="(item, index) in functionList"
				:key="item.id || index"
				class="function-item"
				:class="{ 'function-item--disabled': item.disabled }"
				@click="handleItemClick(item, index)"
			>
				<!-- 图标容器 -->
				<view class="function-icon-wrapper">
					<!-- 图片图标 -->
					<image
						v-if="item.image"
						:src="item.image"
						:mode="item.imageMode || 'aspectFit'"
						class="function-image"
						:class="{ 'function-image--disabled': item.disabled }"
						:style="item.imageStyle || {}"
					/>
					<!-- 字体图标 (兼容) -->
					<uv-icon
						v-else-if="item.icon"
						:name="item.icon"
						:color="item.disabled ? '#c0c4cc' : item.iconColor || '#20a50a'"
						:size="item.iconSize || '32'"
						:custom-style="item.iconStyle || {}"
					/>
				</view>

				<!-- 文字标签 -->
				<text class="function-label" :class="{ 'function-label--disabled': item.disabled }">
					{{ item.label }}
				</text>

				<!-- 角标提示 -->
				<view v-if="item.badge" class="function-badge">
					<text class="badge-text">{{ item.badge }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { computed, defineProps, defineEmits } from 'vue';

	// Props 定义
	const props = defineProps({
		// 功能列表数据
		functionList: {
			type: Array,
			default: () => [],
		},
		// 列表标题
		title: {
			type: String,
			default: '',
		},
		// 每行显示的列数 (4-5)
		columns: {
			type: Number,
			default: 5,
			validator: (value) => value >= 4 && value <= 5,
		},
		// 是否启用响应式布局
		responsive: {
			type: Boolean,
			default: true,
		},
		// 自定义样式
		customStyle: {
			type: [Object, String],
			default: () => ({}),
		},
		// 自定义类名
		customClass: {
			type: String,
			default: '',
		},
		// 是否显示编辑按钮
		showEdit: {
			type: Boolean,
			default: false,
		},
	});

	// Events 定义
	const emit = defineEmits(['itemClick', 'click', 'editClick']);

	// 计算网格类名
	const gridClass = computed(() => {
		const classes = [];

		if (props.responsive) {
			classes.push('function-grid--responsive');
		}

		classes.push(`function-grid--columns-${props.columns}`);

		if (props.customClass) {
			classes.push(props.customClass);
		}

		return classes;
	});

	// 处理项目点击事件
	const handleItemClick = (item, index) => {
		// 如果项目被禁用，不触发点击事件
		if (item.disabled) {
			return;
		}

		// 触发点击事件
		emit('itemClick', { item, index });
		emit('click', { item, index });

		// 如果项目有自定义点击处理函数，执行它
		if (typeof item.onClick === 'function') {
			item.onClick(item, index);
		}
	};

	// 处理编辑按钮点击事件
	const handleEditClick = () => {
		emit('editClick');
	};
</script>

<style lang="scss" scoped>
	@import '@/uni_modules/uv-ui-tools/theme.scss';

	.function-list-container {
		width: 100%;
		padding: 24rpx;
		box-sizing: border-box;
		background: var(--bg-color);
	}

	/* 标题样式 */
	.function-list-header {
		margin-bottom: 32rpx;
		padding: 0 16rpx;
		display: flex;
		align-items: center;

		.header-title {
			font-size: 32rpx;
			font-weight: 600;
			color: $uv-main-color;
			line-height: 1.4;
		}

		.edit-button {
			display: flex;
			align-items: center;
			gap: 8rpx;
			padding: 8rpx 16rpx;
			border-radius: 20rpx;
			background-color: rgba(144, 147, 153, 0.1);
			cursor: pointer;
			transition: all 0.3s ease;

			&:active {
				background-color: rgba(144, 147, 153, 0.2);
				transform: scale(0.95);
			}

			/* #ifdef H5 */
			&:hover {
				background-color: rgba(144, 147, 153, 0.15);
			}
			/* #endif */

			.edit-text {
				font-size: 24rpx;
				color: #909399;
				line-height: 1;
			}
		}
	}

	/* 网格布局 */
	.function-grid {
		display: grid;
		gap: 2rpx;
		width: 100%;

		/* 5列布局 */
		&.function-grid--columns-5 {
			grid-template-columns: repeat(5, 1fr);
		}

		/* 4列布局 */
		&.function-grid--columns-4 {
			grid-template-columns: repeat(4, 1fr);
		}

		/* 响应式布局 */
		&.function-grid--responsive {
			/* 小屏幕显示4列 */
			@media (max-width: 750rpx) {
				grid-template-columns: repeat(4, 1fr);
				gap: 4rpx;
			}

			/* 超小屏幕显示3列 */
			@media (max-width: 600rpx) {
				grid-template-columns: repeat(3, 1fr);
				gap: 2rpx;
			}
		}
	}

	/* 功能项样式 */
	.function-item {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 12rpx 2rpx;
		transition: all 0.3s ease;
		cursor: pointer;
		min-height: 80rpx;

		/* 正常状态 */
		&:active {
			transform: scale(0.95);
			background-color: rgba(32, 165, 10, 0.1);
			border-color: rgba(32, 165, 10, 0.2);
		}

		/* 禁用状态 */
		&.function-item--disabled {
			opacity: 0.5;
			cursor: not-allowed;
			background-color: rgba(192, 196, 204, 0.1);

			&:active {
				transform: none;
				background-color: rgba(192, 196, 204, 0.1);
				border-color: transparent;
			}
		}

		/* Hover效果 (H5端) */
		/* #ifdef H5 */
		&:hover:not(.function-item--disabled) {
			background-color: rgba(32, 165, 10, 0.05);
			border-color: rgba(32, 165, 10, 0.1);
			transform: translateY(-2rpx);
			box-shadow: 0 8rpx 24rpx rgba(32, 165, 10, 0.15);
		}
		/* #endif */
	}

	/* 图标容器 */
	.function-icon-wrapper {
		margin-bottom: 6rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 64rpx;
		height: 64rpx;
		border-radius: 50%;
		background-color: rgba(255, 255, 255, 0.8);
		transition: all 0.3s ease;
		overflow: hidden;

		.function-item:active & {
			background-color: rgba(255, 255, 255, 1);
			transform: scale(1.1);
		}

		/* #ifdef H5 */
		.function-item:hover & {
			background-color: rgba(255, 255, 255, 1);
			transform: scale(1.05);
		}
		/* #endif */
	}

	/* 功能图片样式 */
	.function-image {
		width: 48rpx;
		height: 48rpx;
		border-radius: 8rpx;
		transition: all 0.3s ease;

		&.function-image--disabled {
			opacity: 0.5;
			filter: grayscale(100%);
		}

		.function-item:active & {
			transform: scale(1.1);
		}

		/* #ifdef H5 */
		.function-item:hover & {
			transform: scale(1.05);
		}
		/* #endif */
	}

	/* 文字标签 */
	.function-label {
		font-size: 24rpx;
		color: $uv-content-color;
		text-align: center;
		line-height: 1.3;
		font-weight: 500;
		max-width: 100%;
		word-break: break-all;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;

		&.function-label--disabled {
			color: $uv-light-color;
		}
	}

	/* 角标样式 */
	.function-badge {
		position: absolute;
		top: 8rpx;
		right: 8rpx;
		background-color: #ff4757;
		border-radius: 20rpx;
		min-width: 32rpx;
		height: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 8rpx;

		.badge-text {
			color: #ffffff;
			font-size: 20rpx;
			font-weight: 600;
			line-height: 1;
		}
	}
</style>
